﻿<?xml version="1.0" encoding="utf-16"?>
<Project ToolsVersion="Current">
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp/obj/$(Configuration)/$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <LangVersion>9.0</LangVersion>
    <Configurations>Debug;Release</Configurations>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <RootNamespace>GameLogic</RootNamespace>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>GameLogic</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp/bin/Debug/</OutputPath>
    <DefineConstants>UNITY_2022_3_52;UNITY_2022_3;UNITY_2022;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION;ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT;ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE;PLATFORM_IOS;TEXTCORE_1_0_OR_NEWER;ENABLE_RUNTIME_GI;ENABLE_GAMECENTER;ENABLE_NETWORK;ENABLE_IOS_ON_DEMAND_RESOURCES;ENABLE_IOS_APP_SLICING;PLAYERCONNECTION_LISTENS_FIXED_PORT;DEBUGGER_LISTENS_FIXED_PORT;PLATFORM_SUPPORTS_ADS_ID;SUPPORT_ENVIRONMENT_VARIABLES;PLATFORM_SUPPORTS_PROFILER;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_ETC_COMPRESSION;UNITY_IPHONE_API;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;UNITY_IOS;PLATFORM_IPHONE;UNITY_IPHONE;UNITY_HAS_GOOGLEVR;ENABLE_SPATIALTRACKING;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_OSX;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;DOTWEEN;MOREMOUNTAINS_INTERFACE;MOREMOUNTAINS_TOPDOWNENGINE;MOREMOUNTAINS_INVENTORYENGINE;COZY_WEATHER;COZY_3_AND_UP;COZY_URP;TextMeshPro;ENABLE_LOG;ENABLE_HYBRIDCLR;ENABLE_URP;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER;YOO_ASSET_2;YOO_ASSET_2_3;YOO_ASSET_2_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp/bin/Release/</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.22</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>iOS:9</UnityBuildTarget>
    <UnityVersion>2022.3.52f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="/Users/<USER>/.vscode/extensions/visualstudiotoolsforunity.vstuc-1.1.2/Analyzers/Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="/Users/<USER>/Documents/Unity/PokemonUnity/Pets_SanGuo/Library/PackageCache/dev.yarnspinner.unity@be66087b21/SourceGenerator/YarnSpinner.Unity.SourceCodeGenerator.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="/Users/<USER>/Documents/Unity/PokemonUnity/Pets_SanGuo/Assets/TEngine/Runtime/Core/GameEvent/SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/Toast/ToastWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/BaseListViewCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Map/NinDraggableItemData.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Role/NinChatacterOprationTip.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/BattleMapMaping.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/BattleTeamSelectIndexUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Partial/NInInventorySlot.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ObjectPool/SkillEffectManager.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/TeamStore/NinTeamStoreInventoryUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Map/NinDraggableListUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Role/NinEnvironmentChecker.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ThirdParty/InventoryEngineExtensions/InventoryDragAndDrop/InventoryDragAndDrop.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/SingletonSystem/SingletonSystem.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Battler/BattleMoverAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Tool/PokemonSearcher.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Match/MatchBattleMenuUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/TeamStore/NinTeamStoreContributeUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/TeamStore/NinTeamStoreMenuType.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Executor/BattleMinorActionExecutor.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Mover/NinLocalMoveModel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ThirdParty/InventoryEngineExtensions/MouseControls3D/MouseControls3D.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditMoveUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/GameContext.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/GameConfig.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/FilterPoke/SaleFilterUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Quick/MainMenuAroundPokesUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/FilterPoke/SaleMenuType.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Summon/SummonHistoryPokesWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Item/NinBattleItemModel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Email/NinEmailUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokemonAbilityBtnUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Match/MatchMainMenuSelectUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Filter/MenuSubListItem.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditDetailInfoView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/PokePro/NinBattleAbilityModel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditChangeResultView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeBattleInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ShaderReplace/WaterRunnel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Map/BigMap/NinBigMapPoint.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Mover/NinShowMoveModel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditStatCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Match/MatchBattleTeamsInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/ButtonListener.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Map/BigMap/NinBigMapPickUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Input/NinMainMenuInputMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinPokeBoxGridViewCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditAbilityUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/Light/WindowLightComponent.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Animator/NinSpriteAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Setting/NinSettingCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/FilterPoke/FilterPokeMenuUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Role/Direction.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Animator/Base/SpriteAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Summon/SummonPokesWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/LocalNPC/LocalNPC.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Animator/NinCharacterAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Battler/BattleFieldAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Role/Old/NinRoleLoc.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Email/NinEmailWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/SaleEdit/SaleEditView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Pet/PetsPet.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ThirdParty/InventoryEngineExtensions/MouseControls3D/AIDecisionTargetHasHealth.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Move/BattleMenuMoveListUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Map/BigMap/NinInstanceMapBigMapPickContentUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/BattleRoleManager.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/BattleController.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ShaderReplace/TextureWobble.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/FilterPoke/FilterStatUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/FilterPoke/FilterStatCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/TransmitMap/NinMapPoint.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Yarn/NinYarnSelectedItem.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Map/Chat/ChatMsgDataMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeTypeListUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/SingletonSystem/SingletonBehaviour.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinTopDownInventory.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Animator/NinTeraAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Test/ReplayBattle.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Match/MatchMainWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinTestBox.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Tool/PokeStatTool.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Input/NinSmartNavMover.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Input/NinInputManager.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Extension/GridView+Refresh.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/Load/InstanceMapInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Trainer/NinGroupInfoWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Battler/INinBattlerAnimatorInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeInfoGenderBtnUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Quest/NinYarnStore.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/TransitionAnimator/TransitionAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Cloth/NinChangeClothCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Summon/SummonPokesUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Module/UIModule/UIModule.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinInventory.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Animator/NinBattleCharacterAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/Load/MainMapInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinSwopUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Map/Chat/ChatModel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ResourceLoad/NetResourceMgr/NetResourceMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/Alter/IconTitleAlertView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokemonAbilityUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinSwopUIWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Map/Chat/ChatViewCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Summon/SummonPokesFilterView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/BaseVCWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Tool/PokemonExperienceCalculator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ShaderReplace/DynamicMaterialReplacer.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Mover/NinBattleMoveModel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/InnerLoading.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Summon/SummonLockPokeView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Datas/PokeDataMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/TeamStore/NinTeamStoreWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Map/MapChatacterOprationUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinInventoryCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Battler/NinBattleBall.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Map/NinDraggableCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Battler/BattleTrainer.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ShaderReplace/AutoMaterialReplacer.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/TeamStore/NinTeamStorePokesUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditDetailInfoViewWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Main/MainMenuSelectUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PickEditPokeTypeCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditPickMoveUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Input/NinCharacterPathfindToMouse3D.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/PokeBook/PokemonBookInfoView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditShowStatCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Const/TrainerClothConst.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Map/MapUIWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/Alter/SimpleBattleAlertView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Match/MatchMainMenuViewModel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/Pick/NinPickItemWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/Alter/BaseAlertView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/TransmitMap/TransmitMapComponent.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Login/LeftOrRightSelectUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Cloth/NinClothGridView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PickPokeProperty/PickPokeTypeCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PickCapcCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/Load/IMapInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Yarn/NinYarnCommand.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/MapOtherComponent/MapYarnOprationComponent.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Trainer/NinGroupInfoCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/GameServices.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Quest/NinQuestDataInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/MapOtherComponent/MapInitPositionComponent.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ThirdParty/InventoryEngineExtensions/MouseControls3D/AIDecisionHaveWeaponAndLineOfSightToTargetAndTargetIsInRange3D.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Pet/BattleSyncSpineWithGameCreator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Dialogue/NinDialogueOptionCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Dialogue/NinDialogueBox.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditItemUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/MapOtherComponent/MapNavEditComponent.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/Load/PrefabRegionLoader.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Animator/NinBattleCharacterSpriteAnimatorO.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Input/INinCharacterFollowObject.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Battler/BattleDecision.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Debug/DebugBattleUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Summon/SummonPokeGridCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/BattleSelectTargetUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Evolution/EvolutionController.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Executor/BattleActionProgressExecutor.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeHasTrainerInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/BattleMenuSpecialBtnUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeStatUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Yarn/NinYarnMapMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ResourceLoad/WeatherResourceInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditStatUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/Alter/NinModalWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/FileIO/BattleLocalFile.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/IEvent/ILoginUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Battler/BattlePokeModel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Module/UIModule/UIWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/BattleUIManager.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Move/MoveListViewCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Map/PokeDraggableCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Dialogue/NinDialogueOptionWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Animator/NinBattleBallSpriteAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Executor/BattleActionExecutor.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Dialogue/NinDialogueLinePresenter.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/SaleEdit/SaleEditItemContentView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/TransitionAnimator/TransitionProfile.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Match/MatchBattleCommitWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/MapOtherComponent/MapTriggerEventComponent.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Pet/INinPokeInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditSelectAbilityUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Trainer/NinGroupRequestInfoCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Login/CreateTrainerUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokemonListUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Pet/NinBoxPokeInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Role/NavMeshCharacterMovement.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Const/TrainerNpcMagmaConst.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ShaderReplace/ExpandAndFade.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/TeamStore/NinTeamStoreClothUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Dialogue/NinDialogueOptionUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Breed/NinBreedPokeUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/Load/WaterMapChecker.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/PokePro/INinLocalModel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinTopDownInventoryItem.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/InstanceMap/InstanceMapConfig.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Email/NinEmailDataInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ThirdParty/InventoryEngineExtensions/MouseControls3D/ScriptDrivenHandleWeapon.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/IDraggableMenuComponent.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/Toast/ToastContentCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Module/UIModule/ErrorLogger/ErrorLogger.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/BaseViewCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/SaleEdit/SaleEditPokeContentView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Animator/Base/IBaseAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/BattleUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/TeamStore/NinItemPokesInfoWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/PokePro/NinBattleNatureModel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinInventoryItem.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Const/BattleConst.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Map/Chat/ChatViewUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Role/NinCharacterTopUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/Load/MapData.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/NinLayoutGroupView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinInventoryWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Const/GameEventType.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Formation/FormationInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/TableListView/TableListView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditMoveCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/PreviousOrNextUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Match/MatchBattleCommitUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Login/SelectFirstPokemonUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditSelectCapUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Role/Old/PetsRole.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ObjectPool/ObjectPool.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ShaderReplace/WaterfallSplash.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/FilterPoke/FilterPokeListView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeStatusInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/TransitionAnimator/Misc.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Debug/DebugWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/TestBehaviour.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/Before/BattlePrepareWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeInfoGenderUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Extension/RectTransform+Layout.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PickEditPokeNatureCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Partial/NinInventory.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Battler/NinBattlerModel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Executor/BattleMajorActionExecutor.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ResourceLoad/TrainerResourceInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Const/TrainerNpcRocketConst.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokemonHpStatus.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Dialogue/NinDialogueOptionPresenter.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/TransitionAnimator/MapTransitionAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Partial/NinSerializedInventory.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/Load/MapLoader.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ShaderReplace/SeaLineShader.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Map/Chat/ChatSender.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Setting/NinSettingConfig.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Const/BattlePSEffect.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/PokeSelectTeamInfoCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Login/SelectTrainerBtn.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Yarn/NinYarnMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/MapOtherComponent/MapRuntimeObstacle.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Tool/UnityMainThreadDispatcher.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ShaderReplace/WaterNose.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Pet/PokeManager.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PickPokeProperty/PickPokeNatureCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Role/Old/Billboard.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/IDragDelegate.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/TeamStore/NinTeamTool.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Dialogue/NinCharacterDutyMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinPokeBoxIndexListView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/SummaryItemInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ResourceLoad/ISpriteResourceInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/TeamStore/NinTeamStoreUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Pet/SpineFaceCamera.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Cloth/NinPreviewOldNinClothUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/Load/NPCLoader.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Dialogue/NinDialoguePresenter.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ResourceLoad/ImageResourceLoader.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinContextMenuItem.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeItemUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinMenuItemTypeListView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Tool/PokemonStatCalculator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Filter/MenuSubInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/PlatformInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Pet/NinLocalPokeInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Main/NinMainMenuWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/PokePro/NinBattlePokeTypeModel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Map/NinSysNotificationItem.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Quest/NinQuestWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Datas/InventoryDataMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/MapOtherComponent/NinMapNPCComponent.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Map/MapMenuUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Map/SysNotifacationUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Login/SelectTrainerExteriorUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Extension/ImageLoadExtension.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Quest/NinQuestMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ThirdParty/InventoryEngineExtensions/MouseControls3D/AIActionUseWeapon.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Filter/MenuSubListCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokemonTeamUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Trainer/TrainerInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/Before/BattleLoadingWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/Alter/AlertWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Module/UIModule/ErrorLogger/LogUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ShaderReplace/TextureXMove.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PickEditPokeTypeUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Tool/PokemonType.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeMoveInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeSimpleShowNatureUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ResourceLoad/DefaultResourceInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Datas/TrainerDataMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Cloth/NinClothWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/FilterPoke/FilterPokeEditProperty.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/SingletonSystem/Singleton.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/Load/MapEventObjectLoader.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/Filter/NinFilterPokeViewCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Trainer/NinGroupRequestInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/SimpleListViewCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/NinDataCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/BattleService.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ShaderReplace/WindEffectTree.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Evolution/EvolutionUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/BattleInfoTextSimpleItem.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Setting/NinSettingUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Main/MainMenuSelectCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/Light/WindowLightManager.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/OprationInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Input/NinInputContext.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Cloth/NinChangeClothUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/Load/RuntimeNavMeshLoader.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Executor/BattleActionInitializationExecutor.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Module/UIModule/IUIResourceLoader.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditNatureUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/Before/BattleTeamPreviewUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/TeamStore/NinTeamStoreMenuSelectUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Battler/BattleWeatherAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Const/EffectConstText.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Dialogue/NinDialogueZone.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PickPokeProperty/PickPokeNatureUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeSimpleShowAbilityUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ShaderReplace/OceanWaves.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Role/NinCharacter.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ShaderReplace/TextureTileToScale.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ConstResourcesLoader.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Battler/NinBattleFieldPokeController.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/GameConst.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Summon/SummonPokesGridView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/Pick/NinPickItemUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/GameModule.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinPokeBoxIndexListViewCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ResourceLoad/PngAnimationLoad.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/LoadSceneWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Role/NinCharacterCheckCube.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Mover/NinBattleMover.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ShaderReplace/WaterfallLine.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Animator/Base/Texture2DSheetAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Role/SyncNinWithGameCreator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Extension/CinemachineCamera+Fov.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/PokeTeamBallsUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/MapTilePrefabComponent.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ShaderReplace/OceanScrolling.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Tool/LocalizationTool.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Evolution/EvolutionChecker.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Module/UIModule/UIWidget.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinInventoryMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Role/Old/BoundaryChecker.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/NinListView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Move/MoveInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/BattleFieldVirtualRole.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/Load/PrefabRegionLoader copy.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/MapController.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/FilterPoke/BoxPokeDetail.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/NinListContextMenu.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Dialogue/NinDialogueLineAdvancer.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Animator/NinZmoveAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ResourceLoad/PokemonTypeResourceInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/BattleField.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/BattleMessageHandler.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Mover/INinMoveModel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Role/Old/SyncSpineWithGameCreator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/NumberSliderUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeTypeUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PickEditPokeNatureUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PickPokeProperty/PickPokeTypeUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/ExchangeBoxPokeMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Breed/NinBreedItemsUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/WeatherMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/BattleInfoPokeListUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/Alter/SaleAlertView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/AnimatedTextures.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Const/TrainerNpcAquaConst.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PickFixedItemUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/InstanceMap/InstanceMapMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/ClearBtnInputField.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Extension/GameObject+Layout.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PickPokeProperty/PokeSelectAbilityUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Map/MapMenuBtns.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Main/NinMainMenuUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/BattleTool.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/Pick/NinPickPokeWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Match/MatchBattleCommitConfigInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Pet/BattlePetsRole.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/Pick/NinPickPokeBoxUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/BattlePlayer.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Main/MainMenuUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Quest/NinQuestUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Filter/MenuBoxItemDetail.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/Load/MapTileChecker.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/BattleInfoTextUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/NinSimpleButton.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Module/UIModule/WindowAttribute.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/MapOtherComponent/MapInitPosition.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/TeamStore/NinTeamStoreMenuSelectCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ThirdParty/InventoryEngineExtensions/MouseControls3D/AIActionStandStill.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Match/MatchMainMenuUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinPokeBoxGridView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Battler/NinBattlerModelOtherModel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Battler/BattlePsPokeModel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Player/PetsPlayer.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Evolution/LevelUpLearnMoveUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/LoadingWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ResourceLoad/VFXPrefabLoad.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Extension/TMProExtension.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Trainer/NinGroupInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Login/SelectTrainerUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Trainer/NinGroupMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinMenuItemTypeListCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Extension/MainServerProtoExtension.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Animator/NinMegaAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/UIContext.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ResourceLoad/PokeballAnimationResourceInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/Filter/NinFilterPokeView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/FilterPoke/NinSaleUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/BattleContext.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ShaderReplace/WindEffectGrass.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/NinCanvas.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeFormUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/FilterPoke/FilterPokeAdvenceUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/INinDataView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ResourceLoad/FontResourceMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/WaitOpBoxItem.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Summon/SummonFluteUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Module/UIModule/UIBase.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/BattleMenuUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/mgGif.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Login/TestSelectTrainerWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Extension/PokeDataLocalizationStoreValue+Localization.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/GameApp.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/Load/WaterMapCheckerOld.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Battler/NinPokeBattler.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/ItemInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/TableListView/TableListViewCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/SelectComponent.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Breed/NinBreedItemCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Extension/TrainerExtension.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Tool/JsonHelper.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Match/MatchMenuUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/FilterPoke/SalePokesUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Animator/NinPokeBattlerAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PickCapUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Evolution/EvolutionWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Loading.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ResourceLoad/AssertResourceLoader.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/TransmitMap/NinTransferMapMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/SimpleListDataCell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/TeamStore/NinItemPokesInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeFormBtnUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Item/NinUseItemMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Match/MatchBattlePokemonTeamUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditGmaxUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/FilterPoke/FilterPokeValueDetailUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/Pet/NinNetPokeInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinItemUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Animator/NInBattleCharacterSpriteAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/PokeTeamListInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/NinSysNotificationMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/BattleFieldPokeListUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ObjectPool/SkillCaster.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/Music/SoundPlayer.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/PokePro/NinLocalModel.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/NinGridView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/BattleUIMainWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Animator/NinMaxAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Login/SelectTrainerWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/TeamStore/NinInventoryGridUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Role/NinPlotChatacter.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Battle/BattleInfoUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Animator/UI/ImageSpriteAnimator.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Battle/VFXPrefabsMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/FilterPoke/NinAddSubSlider.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Map/MapCharacterMgr.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Common/BaseVC.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Trainer/NinGroupRequestInfoWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Main/MainMenuContentView.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Input/NinSmartNavFollower.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinPokeBoxUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/LocalNPC/TransferNPC.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Breed/NinBreedPokeWindow.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/NinBox/NinInventorySlot.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditPokeTypeListUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Role/NinChatacterTransfer.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/ResourceLoad/PokemonResourceInfo.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeEdit/PokeEditShowStatUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Pokemon/PokeSpeciesUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/Item/ItemEnum.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Map/MapAroundPokeListUI.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/Menu/Quick/MainMenuAroundPokesUICell.cs" />
    <Compile Include="Assets/GameScripts/HotFix/GameLogic/Scripts/UI/FilterPoke/SalePokeCell.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets/GameScripts/HotFix/GameLogic/GameLogic.asmdef" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ProfilerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.OSXStandalone.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.Protobuf">
      <HintPath>Assets/AssetRaw/DLL/Google.Protobuf.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog.Enrichers.WithCaller">
      <HintPath>Assets/AssetRaw/DLL/Serilog.Enrichers.WithCaller.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.YamlDotNet">
      <HintPath>Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CodeAnalysis">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Editor/Analysis/DLLs/Microsoft.CodeAnalysis.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Satori">
      <HintPath>Assets/Plugins/Nakama/Runtime/Plugins/Satori.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog">
      <HintPath>Assets/AssetRaw/DLL/Serilog.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.System.Text.Encodings.Web">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.System.Text.Encodings.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.DiagnosticSource">
      <HintPath>Assets/AssetRaw/DLL/System.Diagnostics.DiagnosticSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library/PackageCache/com.unity.collections@2.5.1/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="YarnSpinner.Compiler">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/YarnSpinner.Compiler.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library/PackageCache/com.unity.ext.nunit@2.0.3/net40/unity-custom/nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.NET.StringTools">
      <HintPath>Assets/AssetRaw/DLL/Microsoft.NET.StringTools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ReportGeneratorMerged">
      <HintPath>Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>Assets/TEngine/Libraries/System.Runtime.CompilerServices.Unsafe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog.Sinks.Console">
      <HintPath>Assets/AssetRaw/DLL/Serilog.Sinks.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Immutable">
      <HintPath>Assets/AssetRaw/DLL/System.Collections.Immutable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="MessagePack">
      <HintPath>Assets/AssetRaw/DLL/MessagePack.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="MessagePack.Annotations">
      <HintPath>Assets/AssetRaw/DLL/MessagePack.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Metadata">
      <HintPath>Assets/AssetRaw/DLL/System.Reflection.Metadata.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/unityplastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets/Plugins/DOTween/DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nakama">
      <HintPath>Assets/Plugins/Nakama/Runtime/Plugins/Nakama.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Ben.Demystifier">
      <HintPath>Assets/AssetRaw/DLL/Ben.Demystifier.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="YarnSpinner">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/YarnSpinner.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog.Sinks.Debug">
      <HintPath>Assets/AssetRaw/DLL/Serilog.Sinks.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/log4netPlastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.IonicZip">
      <HintPath>Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces">
      <HintPath>Assets/AssetRaw/DLL/Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="dnlib">
      <HintPath>Library/PackageCache/com.code-philosophy.hybridclr@9429a4d24d/Plugins/dnlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LZ4">
      <HintPath>Library/PackageCache/com.code-philosophy.hybridclr@9429a4d24d/Plugins/LZ4.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenEditor">
      <HintPath>Assets/Plugins/DOTween/Editor/DOTweenEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.Microsoft.Bcl.AsyncInterfaces">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS_I2Loc.Xcode">
      <HintPath>Assets/TEngine/Editor/Localization/Unity XCode/UnityEditor.iOS_I2Loc.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library/PackageCache/com.unity.nuget.newtonsoft-json@3.2.1/Runtime/Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.TextureAssets">
      <HintPath>Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library/PackageCache/com.unity.nuget.mono-cecil@1.11.4/Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.System.Threading.Tasks.Extensions">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.Microsoft.Extensions.FileSystemGlobbing">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.Microsoft.Extensions.FileSystemGlobbing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Channels">
      <HintPath>Assets/AssetRaw/DLL/System.Threading.Channels.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.System.Text.Json">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.System.Text.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CodeAnalysis.CSharp">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Editor/Analysis/DLLs/Microsoft.CodeAnalysis.CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog.Sinks.File">
      <HintPath>Assets/AssetRaw/DLL/Serilog.Sinks.File.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Unity.TextMeshPro.csproj" />
    <ProjectReference Include="TEngine.Runtime.csproj" />
    <ProjectReference Include="GameProto.csproj" />
    <ProjectReference Include="UniTask.csproj" />
    <ProjectReference Include="YooAsset.csproj" />
    <ProjectReference Include="Unity.Mathematics.csproj" />
    <ProjectReference Include="PetsCore.csproj" />
    <ProjectReference Include="Unity.Cinemachine.csproj" />
    <ProjectReference Include="Plugins.csproj" />
    <ProjectReference Include="spine-unity.csproj" />
    <ProjectReference Include="MoreMountains.TopDownEngine.csproj" />
    <ProjectReference Include="MoreMountains.InventoryEngine.csproj" />
    <ProjectReference Include="MoreMountains.Interface.csproj" />
    <ProjectReference Include="MoreMountains.TopDownEngine.InputSystem.csproj" />
    <ProjectReference Include="Unity.Burst.csproj" />
    <ProjectReference Include="MoreMountains.Tools.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.Runtime.csproj" />
    <ProjectReference Include="DistantLands.Cozy.Runtime.csproj" />
    <ProjectReference Include="YarnSpinner.Unity.csproj" />
    <ProjectReference Include="MessagePack.Unity.csproj" />
    <ProjectReference Include="UnityEditor.UI.csproj" />
    <ProjectReference Include="UnityEngine.UI.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>