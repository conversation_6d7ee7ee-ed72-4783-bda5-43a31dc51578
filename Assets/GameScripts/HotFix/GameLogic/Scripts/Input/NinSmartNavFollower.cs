using UnityEngine;
using UnityEngine.AI;
using System.Collections;
using System.Collections.Generic;

public class NinSmartNavFollower : MonoBehaviour
{
    [Header("核心组件")]
    public NinSmartNavMover smartNavMover;

    [Header("跟随配置")]
    private Transform leader;           // 主角
    private int slotIndex = 0;          // 插槽编号（0=最靠近）
    private float slotSpacing = 1.5f;   // 插槽间距
    private float updateRate = 0.2f;    // 更新频率
    private float closeDistance = 0.5f; // 停止移动判定
    private float directionUpdateThreshold = 1.5f; // 主角走这么远才更新方向
    private float interpolationSpeed = 5f;         // 插值平滑速度

    [Header("调试")]
    private bool showDebugGizmos = true;

    private Coroutine followRoutine;
    private Vector3 lastLeaderPos;
    private Vector3 lastLeaderDir; // 缓存的主角行进方向
    private Vector3 targetPos;     // 当前目标点

    public bool IsMoving => followRoutine != null;
    // public NinSmartNavMover smartNavMover;
    // private Transform leader;              // 主角
    // private int slotIndex;                 // 插槽编号（0=最近）
    // private float slotSpacing = 1.5f;      // 插槽间距
    // private float updateRate = 0.2f;       // 更新频率
    // private float closeDistance = 0.5f;    // 靠近判定
    // private float _moveSpeed = 4f;
    // private Vector3 lastLeaderPos;
    // private Vector3 lastLeaderDir;

    // // private NavMeshAgent agent;
    // private Coroutine followRoutine;
    // IgnoreDetectMouse

    // void Awake()
    // {
    //     agent = GetComponent<NavMeshAgent>();
    //     agent.speed = moveSpeed;
    //     agent.updateRotation = true;
    // }
    public Vector3 GetMoveDirection()
    {
        return smartNavMover.GetMoveDirection();
    }
    // public bool IsMoving
    // {
    //     get
    //     {
    //         return followRoutine != null;
    //     }
    // }
    public void SetCloseDistance(float distance)
    {
        closeDistance = distance;
    }
    public void SetLocInfo(MainServer.TrainerLocInfo locInfo)
    {
        //链路地址
        if (locInfo == null)
        {
            return;
        }
        var locs = new List<MainServer.TrainerLoc>();
        foreach (var loc in locInfo.LocLine)
        {
            if (loc.MainLandType == locInfo.Loc.MainLandType && loc.MapName == locInfo.Loc.MapName)
            {
                locs.Add(loc);
            }
        }
        if (locs.Count == 0)
        {
            locs.Add(locInfo.Loc);
        }
        smartNavMover.SetNewTargetLocs(locs.ToArray());
    }

    
    public void ResetPath()
    {
        // StopFollow();
        smartNavMover.StopMoving();
    }

    void OnEnable()
    {
        if (leader != null)
        {
            StartFollow();
        }
    }

    public void SetLeader(Transform newLeader, int newSlotIndex)
    {
        if (newLeader == null)
        {
            StopFollow();
            return;
        }
        leader = newLeader;
        slotIndex = newSlotIndex;
        StartFollow();
    }

    public void SetMoveSpeed(float speed)
    {
        smartNavMover.SetMoveSpeed(speed);
    }

    public void StartFollow()
    {
        StopFollow();
        followRoutine = StartCoroutine(FollowLeader());
    }

    public void StopFollow()
    {
        if (followRoutine != null)
        {
            StopCoroutine(followRoutine);
            followRoutine = null;
        }
        smartNavMover.StopMoving();
    }

    private IEnumerator FollowLeader()
    {
        lastLeaderPos = leader.position;
        lastLeaderDir = leader.forward;

        while (true)
        {
            if (leader != null)
            {
                // 检查 leader 是否移动了足够距离
                float leaderMoved = Vector3.Distance(leader.position, lastLeaderPos);
                if (leaderMoved > directionUpdateThreshold)
                {
                    // 根据主角移动方向更新阵型方向
                    Vector3 moveDir = (leader.position - lastLeaderPos).normalized;
                    if (moveDir.sqrMagnitude > 0.01f)
                    {
                        lastLeaderDir = moveDir;
                    }
                    lastLeaderPos = leader.position;
                }

                // 计算插槽位置
                Vector3 leaderRight = Vector3.Cross(Vector3.up, lastLeaderDir);
                int row = (slotIndex / 2) + 1;
                int side = (slotIndex % 2 == 0) ? -1 : 1; // 左右交替
                Vector3 offset = (lastLeaderDir * -slotSpacing * row) + (leaderRight * side * slotSpacing);

                // 目标位置（平滑插值）
                Vector3 desiredPos = leader.position + offset;
                targetPos = Vector3.Lerp(targetPos == Vector3.zero ? transform.position : targetPos, desiredPos, Time.deltaTime * interpolationSpeed);

                // 距离检查
                if (Vector3.Distance(transform.position, targetPos) > closeDistance)
                {
                    smartNavMover.MoveToPosition(targetPos);
                }
                else
                {
                    smartNavMover.StopMoving();
                }
            }

            yield return new WaitForSeconds(updateRate);
        }
    }

    private void OnDrawGizmos()
    {
        if (showDebugGizmos && leader != null)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawSphere(targetPos, 0.2f);
            Gizmos.DrawLine(transform.position, targetPos);
        }
    }
}