using UnityEngine;
using MoreMountains.TopDownEngine;
using System.Collections;
using UnityEngine.AI;
using Cysharp.Threading.Tasks;
using System;
using System.Linq;

/// <summary>
/// 处理角色传送的组件，支持跨地图传送和NavMesh导航
/// </summary>
public class NinChatacterTransfer : MonoBehaviour
{
    [Header("References")]
    public NinCharacter ninCharacter;
    public float lastTransferTime;
    public bool isTransferring {  get; private set; } = false;
    private string _transferringTargetMapName;
    private Vector3 _previousCharacterPosition;
    void Start()
    {
        if (ninCharacter == null)
        {
            ninCharacter = GetComponentInParent<NinCharacter>();
        }
    }
    
    /// <summary>
    /// 获取当前正在传送的目标地图名称
    /// </summary>
    public string TransferringTargetMapName {
        get {
            return _transferringTargetMapName;
        }
    }
    
    /// <summary>
    /// 设置传送的目标地图
    /// </summary>
    public void SetTransferringMap(string mapName)
    {
        if(isTransferring) {
            return;
        }
        _transferringTargetMapName = mapName;
    }
    
    /// <summary>
    /// 检查并清除传送目标地图
    /// </summary>
    /// <returns>如果进入的是目标地图则返回true</returns>
    public bool ClearTransferringMapIfEnterMap(string mapName)
    {
        if(_transferringTargetMapName == mapName)
        {
            _transferringTargetMapName = null;
            return true;
        }
        return false;
    }
    
    /// <summary>
    /// 检查是否可以传送
    /// </summary>
    public bool CanTransfer()
    {
        if(isTransferring && Time.time - lastTransferTime < 1) { //传送中不触发
            return false;
        }
        return ninCharacter.CanTransfer;
        // if(Time.time - lastTransferTime > 3) //3秒内不能再次传送
        // {
        //     return true;
        // }
        // return string.IsNullOrEmpty(_transferringTargetMapName);
    }
    
    /// <summary>
    /// 在编辑器中可视化传送点
    /// </summary>
    private void OnDrawGizmos()
    {
        Gizmos.color = Color.blue;
        Gizmos.DrawWireSphere(transform.position, 0.5f);
    }
    public async UniTask TransferControl(Vector3 position)
    {
        if(ninCharacter == null || ninCharacter.CanTransfer == false) {
            return;
        }
        await Transfer(position);
    }
    /// <summary>
    /// 将角色传送到指定位置
    /// 如果角色有NavMeshAgent组件，会尝试找到最近的可行走点
    /// </summary>
    public async UniTask Transfer(Vector3 position)
    {
        if(ninCharacter == null) {
            return;
        }
        isTransferring = true;
        bool canTransfer = ninCharacter.CanTransfer;
        ninCharacter.CanTransfer = false;
        ninCharacter.Freeze();
        var direction = ninCharacter.GetCurrentDirection();
        lastTransferTime = Time.time;
        _previousCharacterPosition = ninCharacter.transform.position;

        var movement = ninCharacter.GetComponent<CharacterMovement>();
        var navMeshAgent = ninCharacter.GetComponent<NavMeshAgent>();
        // ninCharacter.smartNavFollower.ResetPath();
        // ninCharacter.ninCharacterPathfindToMouse3D.StopPath();

        if (movement != null)
        {
            movement.enabled = false;
            movement.ScriptDrivenInput = false;
        }
        if (navMeshAgent != null)
        {
            navMeshAgent.enabled = false;
        }

        if (navMeshAgent != null)
        {
            Vector3 offsetDir = Role.DirectionHelper.GetOffsetFromDirection(direction);
            float[] sampleDistances = new float[] { 0.01f, 0.1f, 0.3f, 0.5f };
            bool foundValidPosition = false;

            foreach (float distance in sampleDistances)
            {
                Vector3 checkPos = position + offsetDir * distance;

                if (NavMesh.SamplePosition(checkPos, out NavMeshHit hit, 0.1f, NavMesh.AllAreas))
                {
                    ninCharacter.gameObject.transform.position = hit.position;
                    foundValidPosition = true;
                    break;
                }
            }

            if (!foundValidPosition)
            {
                ninCharacter.gameObject.transform.position = position;
            }
        }
        else
        {
            ninCharacter.gameObject.transform.position = position;
        }
        if(await isTransferComplete(position)) {
            // ninCharacter.ninCharacterPathfindToMouse3D.StopPath();
            if (navMeshAgent != null)
            {
                navMeshAgent.enabled = true;
                navMeshAgent.Warp(ninCharacter.gameObject.transform.position);
            }
            movement.enabled = true;
            await UniTask.Yield();
            ninCharacter.UnFreeze();
        }
        // if(ninCharacter.trainer != null) { //不能是精灵
        //     await MapController.Current.mapCharacterMgr.UpdateFollowPokes(ninCharacter.trainer.FollowPoke.Pokes.ToArray(), ninCharacter.trainer.Id);
        // }
        await UniTask.Yield();
        if(ninCharacter.trainer != null) { //不能是精灵
            var followPokes = MapController.Current.mapCharacterMgr.GetFollowPokes(ninCharacter.trainer.Id);
            followPokes.ForEach((poke) => {
                poke.ninChatacterTransfer.Transfer(position).Forget();
            });
        }
        ninCharacter.CanTransfer = canTransfer;
        isTransferring = false;
        // TransferAfter(position, async (completeResult) =>
        // {
        //     if(complete != null) {
        //         complete(completeResult);
        //     }
        //     ninCharacter.ninCharacterPathfindToMouse3D.StopPath();
        //     if (navMeshAgent != null)
        //     {
        //         navMeshAgent.enabled = true;
        //         navMeshAgent.Warp(ninCharacter.gameObject.transform.position);
        //     }
        //     movement.enabled = true;
        //     await UniTask.DelayFrame(10);
        //     ninCharacter.UnFreeze();
        // }).Forget();
    }

    // public bool Transfer(Vector3 position)
    // {
    //     var direction = ninCharacter.GetCurrentDirection();

    //     lastTransferTime = Time.time;
    //     _previousCharacterPosition = ninCharacter.transform.position;
    //     // _character.FindAbility<CharacterMovement>().ScriptDrivenInput = false;
    //     // 获取必要的组件
    //     var movement = ninCharacter.GetComponent<CharacterMovement>();
    //     var navMeshAgent = ninCharacter.GetComponent<NavMeshAgent>();
    //     ninCharacter.ninCharacterPathfindToMouse3D.StopPath();
    //     // 暂时禁用移动和导航组件
    //     if(movement != null) {
    //         movement.enabled = false;
    //         movement.ScriptDrivenInput = false;
    //     }
    //     if (navMeshAgent != null)
    //     {
    //         navMeshAgent.enabled = false;
    //     }
    //     // return;

    //     // 如果有NavMeshAgent，尝试找到最近的可行走点
    //     if (navMeshAgent != null)
    //     {
    //         // 使用较小的采样距离，确保更精确的传送位置
    //         float[] sampleDistances = new float[] { 0.01f, 0.1f, 0.3f, 0.5f };
    //         bool foundValidPosition = false;
            
    //         foreach (float distance in sampleDistances)
    //         {
    //             if (NavMesh.SamplePosition(position, out NavMeshHit hit, distance, NavMesh.AllAreas))
    //             {
    //                 ninCharacter.gameObject.transform.position = hit.position;
    //                 foundValidPosition = true;
    //                 break;
    //             }
    //         }
            
    //         // 如果找不到可行走点，使用原始位置
    //         if (!foundValidPosition)
    //         {
    //             ninCharacter.gameObject.transform.position = position;
    //         }
    //     }
    //     else
    //     {
    //         // 没有NavMeshAgent时直接使用目标位置
    //         ninCharacter.gameObject.transform.position = position;
    //     }

    //     TransferAfter(position, (complete) => {
    //         ninCharacter.ninCharacterPathfindToMouse3D.StopPath();
    //         // 重新启用导航组件
    //         if (navMeshAgent != null)
    //         {
    //             navMeshAgent.enabled = true;
    //             // 使用Warp确保NavMeshAgent正确更新位置
    //             navMeshAgent.Warp(ninCharacter.gameObject.transform.position);
    //         }
    //         movement.enabled = true;
    //     }).Forget();
    //     return true;
    // }
    // private async UniTaskVoid TransferAfter(Vector3 targetPosition, Action<bool> complete)
    // {
    //     if(await isTransferComplete(targetPosition))
    //     {
    //         // ⏱️ 确保相机立即同步位置
    //         // ForceUpdateCameraImmediately();
    //         // ninCharacter.ninCharacterPathfindToMouse3D.StopPath();
    //         complete.Invoke(true);
    //         // _transferringTargetMapName = null;
    //     } else {
    //         complete.Invoke(false);
    //     }
    // }
    // private async UniTask<bool> isTransferComplete(Vector3 targetPosition)
    // {
    //     await UniTask.Yield();
    //     //是否已经到达目标位置//通过距离
    //     if(Vector3.Distance(ninCharacter.gameObject.transform.position, targetPosition) < 3f)
    //     {
    //         return true;
    //     }
    //     return false;
    // }
    private async UniTask<bool> isTransferComplete(Vector3 targetPosition, float timeout = 1.0f)
    {
        const float checkInterval = 0.05f; // 每 50ms 检查一次
        float elapsed = 0f;

        while (elapsed < timeout)
        {
            // 如果位置已经足够接近目标点，视为传送完成
            if (Vector3.Distance(ninCharacter.transform.position, targetPosition) < 3f)
            {
                return true;
            }

            await UniTask.Delay((int)(checkInterval * 1000), DelayType.DeltaTime);
            elapsed += checkInterval;
        }

        // 超时仍未到达，返回 false（可能出错）
        return false;
    }

    private void ForceUpdateCameraImmediately()
    {
        var allCams = GameObject.FindObjectsOfType<Unity.Cinemachine.CinemachineCamera>();
        foreach (var cam in allCams)
        {
            if (cam.isActiveAndEnabled && cam.name == "CM vcam1")
            {
                var delta = ninCharacter.transform.position - _previousCharacterPosition;
                cam.OnTargetObjectWarped(ninCharacter.transform, delta);

                Debug.Log("Warped camera: " + cam.name);
            }
        }

        // 可选：立即刷新 brain
        var brain = Camera.main.GetComponent<Unity.Cinemachine.CinemachineBrain>();
        if (brain != null && brain.UpdateMethod == Unity.Cinemachine.CinemachineBrain.UpdateMethods.ManualUpdate)
        {
            brain.ManualUpdate();
        }
    }

    private System.Collections.IEnumerator MonitorPositionChanges()
    {
        Vector3 lastPosition = ninCharacter.gameObject.transform.position;
        Vector3 lastLocalPosition = ninCharacter.gameObject.transform.localPosition;
        Transform lastParent = ninCharacter.gameObject.transform.parent;
        
        for (int i = 0; i < 10; i++) // Monitor for 10 frames
        {
            yield return new WaitForEndOfFrame();
            
            if (lastPosition != ninCharacter.gameObject.transform.position ||
                lastLocalPosition != ninCharacter.gameObject.transform.localPosition ||
                lastParent != ninCharacter.gameObject.transform.parent)
            {
                Debug.Log($"Frame {i} - Position changed:");
                Debug.Log($"World Position: {ninCharacter.gameObject.transform.position} (was {lastPosition})");
                Debug.Log($"Local Position: {ninCharacter.gameObject.transform.localPosition} (was {lastLocalPosition})");
                Debug.Log($"Parent: {(ninCharacter.gameObject.transform.parent ? ninCharacter.gameObject.transform.parent.name : "null")} (was {(lastParent ? lastParent.name : "null")})");
                
                lastPosition = ninCharacter.gameObject.transform.position;
                lastLocalPosition = ninCharacter.gameObject.transform.localPosition;
                lastParent = ninCharacter.gameObject.transform.parent;
            }
        }
    }
}