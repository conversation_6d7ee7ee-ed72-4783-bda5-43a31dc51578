using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using GameLogic.Scripts.Extension;
using Language.Lua;
using MoreMountains.TopDownEngine;
using PetsServices;
using PokeApiNet;
using UnityEngine;
using UnityEngine.AI;

public class MapCharacterMgr : MonoBehaviour
{
    public NinCharacter OtherCharacterPrefab;
    public NinPlotChatacter NinPlotChatacterPrefab;
    public Transform characterContainer;
    // public NinCharacter NPCCharacterPrefab;
    public Dictionary<long, NinCharacter> TrainerMap = new();
    public Dictionary<long, Dictionary<long, NinCharacter>> followPokeMap = new();
    public Dictionary<string, NinCharacter> NPCMap = new();
    // public GameObject characterBPrefab; // 要放置的角色 B Prefab
    public float spawnDistance = 1f;    // 放置的目标距离
    public float checkRadius = 1f;     // 检测的范围半径
    public int maxAttempts = 10;       // 最大采样尝试次数

    public NinCharacter myCharacter
    {
        get
        {
            if (LevelManager.Instance.Players.Count == 0)
            {
                return null;
            }
            return LevelManager.Instance.Players[0].gameObject.GetComponent<NinCharacter>();
        }
    }
    public void FreezeMyCharacter() {
        myCharacter.Freeze();
    }
    public void UnFreezeMyCharacter() {
        myCharacter.UnFreeze();
    }
    public void TryDefaultDirectionMyCharacter() {
        myCharacter.TrySetCurrentDirection(Role.Direction.S);
    }
    
    public async UniTask InitialMyCharacter(MainServer.Trainer trainer)
    {
        var pos = await trainer.LocPosion();
        // myCharacter.ninChatacterTransfer.Transfer(pos, null);
        myCharacter.SetTrainer(trainer);
        // myCharacter.OpenControlCharacter();
        await MapController.Current.transferMapMgr.MoveToNewMap(myCharacter.ninChatacterTransfer, pos.mapInfo, pos.position);
        // UpdateFollowPokes(trainer.FollowPoke.Pokes.ToArray(), trainer.Id);
    }
    public IEnumerable<NinCharacter> GetOtherPlayers(int prefix) {
        return TrainerMap.Values.Take(prefix);
    }
    public Dictionary<long, MainServer.Trainer> FilterOtherTrainers(MainServer.Trainer[] trainers) {
        var filterTrainers = new Dictionary<long, MainServer.Trainer>();
        foreach (var trainer in trainers)
        {
            long sts = 0;
            if (trainer.SessionInfo.SessionEndTs > 0)
            {
                sts = System.DateTimeOffset.UtcNow.ToUnixTimeSeconds() - trainer.SessionInfo.SessionEndTs;
            }
            //自己或者离线超过60s
            if (myCharacter.trainer != null && trainer.Id == myCharacter.trainer.Id)
            {
                continue;
            }
            if(sts > 60) {
                RemoveOtherTrainer(trainer.Id);
                continue;
            }
            var locInfo = trainer.SessionInfo.LocInfo;
            if(locInfo == null || locInfo.Loc == null || locInfo.Loc.MainLandType != MapController.Current.mapLoader.GetMainLandType()) {
                RemoveOtherTrainer(trainer.Id);
                continue;
            }
            Vector3 position = new Vector3(locInfo.Loc.X, locInfo.Loc.Y, locInfo.Loc.Z);
            if (Vector3.Distance(position, myCharacter.transform.position) > 70f) { //超过距离就移除
                RemoveOtherTrainer(trainer.Id);
                continue;
            }
            filterTrainers[trainer.Id] = trainer;
            // filterTrainers.Add(trainer);
        }
        return filterTrainers;
        // return filterTrainers.ToArray();
    }
    public void RemoveOtherTrainer(long trainerId) {
        if(TrainerMap.TryGetValue(trainerId, out var character)) {
            RemoveFollowPokes(trainerId);
            TrainerMap.Remove(trainerId);
            DestroyImmediate(character.gameObject);
        }
    }
    public async UniTask UpdateOtherTrainers(MainServer.Trainer[] trainers)
    {
        var othreTrainerMap = FilterOtherTrainers(trainers);
        var keys = TrainerMap.Keys;
        foreach (var trainerId in keys)
        {
            if(!othreTrainerMap.ContainsKey(trainerId)) {
                RemoveOtherTrainer(trainerId);
            }
        }
        foreach (var trainer in othreTrainerMap.Values)
        {
            var locInfo = trainer.SessionInfo.LocInfo;
            if(locInfo != null) {
                // if(locInfo.Loc.MapName != MapController.Current.mapLoader.GetMapInfo().GetMapNameId()) {
                //     continue;
                // }
                Vector3 position = new Vector3(locInfo.Loc.X, locInfo.Loc.Y, locInfo.Loc.Z);
                var exists = TrainerMap.TryGetValue(trainer.Id, out var character);
                if (exists && Vector3.Distance(position, character.transform.position) <= 10f)
                {
                    // List<Vector3> positions = new();
                    // foreach (var item in locInfo.LocLine)
                    // {
                    //     positions.Add(new Vector3(item.X, item.Y, item.Z));
                    // }
                    // character.ninCharacterPathfindToMouse3D.SetLocInfo(locInfo);
                    character.smartNavFollower.SetLocInfo(locInfo);
                }
                else if (exists)
                {
                    // character.ninChatacterTransfer.Transfer(position).Forget();
                    MapController.Current.transferMapMgr.MoveToCurrentMap(character.ninChatacterTransfer, position);
                    // character.ninCharacterPathfindToMouse3D.ClearTargetPositions();
                    // character.transform.position = position;
                }
                else
                {
                    // NinCharacter newCharacter = Instantiate(OtherCharacterPrefab, position, Quaternion.identity);
                    // newCharacter.gameObject.SetActive(true);
                    var newCharacter = await CreateNinCharacter(position);
                    newCharacter.SetTrainer(trainer, true);
                    // newCharacter.ninCharacterPathfindToMouse3D.ClearTargetPositions();
                    // newCharacter.transform.position = position;
                    TrainerMap[trainer.Id] = newCharacter;
                    // NinCharacter newPlayer = Instantiate (OtherCharacterPrefab, _initialSpawnPointPosition, Quaternion.identity);
                }
                await UpdateFollowPokes(trainer.FollowPoke.Pokes.ToArray(), trainer.Id);
            } else {
                PLog.Error($"Trainer {trainer.Id} loc {trainer.ActionInfo.Loc} is invalid");
                //  myCharacter.transform.position = Vector3.zero;
            }
        }

    }
    public async UniTask<NinCharacter> CreateNinCharacter(Vector3 position, bool isActive = true) {
        NinCharacter[] newCharacters = await InstantiateAsync(OtherCharacterPrefab, 1, position, Quaternion.identity).ToUniTask();
        // var newCharacter = Instantiate(OtherCharacterPrefab, position, Quaternion.identity);
        var newCharacter = newCharacters[0];
        newCharacter.gameObject.SetActive(isActive);
        newCharacter.transform.SetParent(characterContainer, false);
        return newCharacter;
    }
    public void RemoveFollowPokes(long trainerId) {
        if(followPokeMap.TryGetValue(trainerId, out var pokeCharacters)) {
            foreach (var poke in pokeCharacters.Values)
            {
                DestroyImmediate(poke.gameObject);
            }
            followPokeMap.Remove(trainerId);
        }
    }
    public List<NinCharacter> GetFollowPokes(long trainerId) {
        if(followPokeMap.TryGetValue(trainerId, out var pokeCharacters)) {
            return pokeCharacters.Values.ToList();
        }
        return new List<NinCharacter>();
    }
    public void SyncFollowCharacter(MainServer.Trainer trainer) {
        if(trainer == null || !TrainerMap.TryGetValue(trainer.Id, out var leaderCharacter)) {
            return;
        }
        if (followPokeMap.TryGetValue(trainer.Id, out var pokeCharacters))
        {
            foreach (var poke in pokeCharacters.Values)
            {
                poke.speedType = leaderCharacter.speedType;
            }
        }
        if(trainer.Id != myCharacter.trainer.Id && NinGroupMgr.share.IsLeader(trainer)) {
            myCharacter.speedType = leaderCharacter.speedType;
        }
    }
    public async UniTask UpdateFollowPokes(MainServer.TrainerFollowPokeInfo[] trainerFollowPokes, Int64 trainerId)
    {
        if (!TrainerMap.TryGetValue(trainerId, out var leaderCharacter))
        {
            if(trainerId == myCharacter.trainer.Id) {
                leaderCharacter = myCharacter;
            } else {
                return;
            }
        }
        List<NinCharacter> saveCharacters = new();
        if (!followPokeMap.TryGetValue(trainerId, out var pokeCharacters))
        {
            // saveCharacters.Add(character);
            pokeCharacters = new Dictionary<long, NinCharacter>();
            followPokeMap[trainerId] = pokeCharacters;
        }
        // List<NinCharacter> saveCharacters = new();
        // foreach (var pokeInfo in trainerFollowPokes)
        for(int i = 0; i < trainerFollowPokes.Length; i++)
        {
            var pokeInfo = trainerFollowPokes[i];
            // string id = pokeInfo.Id.ToString();
            if(pokeCharacters.TryGetValue(pokeInfo.Id, out var character))
            {
                saveCharacters.Add(character);
            } else {
                var newPoke = await PlaceCharacterNear(leaderCharacter.transform);
                if(newPoke == null) {
                    continue;
                }
                newPoke.SetPokeInfo(pokeInfo);
                newPoke.smartNavFollower.SetLeader(leaderCharacter.transform, i);
                // newPoke.smartNavFollower.FollowCharacter = leaderCharacter;
                pokeCharacters[pokeInfo.Id] = newPoke;
                saveCharacters.Add(newPoke);
            }
        }
        List<long> keysToRemove = new List<long>();
        foreach (var kvp in pokeCharacters)
        {
            if (!saveCharacters.Contains(kvp.Value))
            {
                kvp.Value.gameObject.SetActive(false);
                Destroy(kvp.Value);
                keysToRemove.Add(kvp.Key); // 延迟移除
            }
        }

        // 统一移除记录的键
        foreach (var key in keysToRemove)
        {
            pokeCharacters.Remove(key);
        }
    }

    /// <summary>
    /// 在角色 A 附近放置角色 B。
    /// </summary>
    /// <param name="character">角色 的 Transform</param>
    public async UniTask<NinCharacter> PlaceCharacterNear(Transform character)
    {
        Vector3 origin = character.position; // 获取角色 A 的当前位置
        for (int i = 0; i < maxAttempts; i++)
        {
            // 在角色 A 附近随机生成一个方向和距离
            Vector2 randomCircle = UnityEngine.Random.insideUnitCircle.normalized * spawnDistance;
            Vector3 targetPosition = origin + new Vector3(randomCircle.x, 0, randomCircle.y);

            // 检查目标点是否在 NavMesh 上
            if (NavMesh.SamplePosition(targetPosition, out NavMeshHit hit, checkRadius, NavMesh.AllAreas))
            {
                // NinCharacter newPoke = Instantiate(OtherCharacterPrefab, hit.position, Quaternion.identity);
                NinCharacter newPoke = await CreateNinCharacter(hit.position);
                // newPoke.gameObject.SetActive(true);
                return newPoke; // 成功放置后退出
                // 确保目标点无障碍物
                // if (!Physics.CheckSphere(hit.position, 0.5f)) // 半径为 0.5 的范围内无碰撞
                // {
                //     // 在检测到的位置实例化角色 B
                //     Instantiate(OtherCharacterPrefab, hit.position, Quaternion.identity);
                //     Debug.Log($"Character B placed at {hit.position}");
                //     return; // 成功放置后退出
                // }
            }
        }

        Debug.LogWarning("Failed to find a valid position for Character B.");
        return null;
    }
    // private void UnloadFarNPCs(Vector3 localPos)
    // {
    //     // for (int i = _loadedNPCs.Count - 1; i >= 0; i--)
    //     // {
    //     //     var npc = _loadedNPCs[i];
    //     //     if (Vector3.Distance(localPos, npc.Position) > unloadRadius)
    //     //     {
    //     //         MapController.Current.mapCharacterMgr.RemoveNpc(npc.Instance);
    //     //         _loadedPositions.Remove(npc.Position);
    //     //         _loadedNPCs.RemoveAt(i);
    //     //     }
    //     // }
    // }

    public async UniTask<NinCharacter> CreateNpc(MainServer.NpcRoleConfig config) {
        var newCharacter = await CreateNinCharacter(Vector3.zero, true);
        newCharacter.InitNpc(config);
        NPCMap[config.Name] = newCharacter;
        return newCharacter;
    }
    public NinPlotChatacter CreateNinPlotChatacter(Vector3 position, bool isActive = true) {
        var newCharacter = Instantiate(NinPlotChatacterPrefab, position, Quaternion.identity);
        newCharacter.gameObject.SetActive(isActive);
        newCharacter.transform.SetParent(characterContainer, false);
        return newCharacter;
    }
    public void RemoveNpc(string characterName) {
        if(NPCMap.TryGetValue(characterName, out var character)) {
            NPCMap.Remove(characterName);
            Destroy(character.gameObject);
        }
    }
    public void RemoveNpc(NinCharacter character) {
        if(NPCMap.ContainsKey(character.characterName)) {
            NPCMap.Remove(character.characterName);
            Destroy(character.gameObject);
        }
    }
    public void RemoveNpc(GameObject gameObject) {
        var character = gameObject.GetComponent<NinCharacter>();
        if(character == null) return;
        if(NPCMap.ContainsKey(character.characterName)) {
            NPCMap.Remove(character.characterName);
            Destroy(character.gameObject);
        }
    }
    public NinDialogueBox? GetNpcCharacterDialogueBox(string npcName) {
        if(!NPCMap.TryGetValue(npcName, out var character)) {
            return null;
        }
        return character.dialogueZone.dialogueBox;
    }
}
